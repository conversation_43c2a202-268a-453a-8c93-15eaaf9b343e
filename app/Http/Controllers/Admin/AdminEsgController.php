<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EsgQuestion;
use App\Models\EsgResponse;
use App\Models\StartupProfile;
use App\Services\EsgScoringService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminEsgController extends Controller
{
    protected EsgScoringService $esgScoringService;

    public function __construct(EsgScoringService $esgScoringService)
    {
        $this->esgScoringService = $esgScoringService;
        $this->middleware(['auth', 'role:admin|super-admin|analyst']);
    }

    /**
     * ESG Management Dashboard
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'ESG Management',
                'url' => route('admin.esg.index'),
                'active' => true
            ],
        ];

        // ESG Overview Statistics
        $stats = [
            'total_questions' => EsgQuestion::count(),
            'active_questions' => EsgQuestion::active()->count(),
            'total_responses' => EsgResponse::count(),
            'completed_assessments' => StartupProfile::whereNotNull('esg_score')->count(),
            'average_score' => StartupProfile::whereNotNull('esg_score')->avg('esg_score') ?? 0,
            'pending_reviews' => StartupProfile::whereNotNull('esg_score')
                ->where('updated_at', '>=', Carbon::now()->subDays(7))
                ->count(),
        ];

        // Category breakdown
        $categoryStats = [
            'environmental' => [
                'questions' => EsgQuestion::byCategory('environmental')->active()->count(),
                'avg_score' => $this->getCategoryAverageScore('environmental'),
            ],
            'social' => [
                'questions' => EsgQuestion::byCategory('social')->active()->count(),
                'avg_score' => $this->getCategoryAverageScore('social'),
            ],
            'governance' => [
                'questions' => EsgQuestion::byCategory('governance')->active()->count(),
                'avg_score' => $this->getCategoryAverageScore('governance'),
            ],
        ];

        // Recent activity
        $recentActivity = collect([
            // Recent ESG completions
            ...StartupProfile::whereNotNull('esg_score')
                ->with('user')
                ->latest('updated_at')
                ->take(5)
                ->get()
                ->map(function ($profile) {
                    return [
                        'type' => 'completion',
                        'description' => "{$profile->user->name} completed ESG assessment",
                        'score' => $profile->esg_score,
                        'created_at' => $profile->updated_at,
                        'user_id' => $profile->user_id,
                    ];
                }),
        ])->sortByDesc('created_at')->take(10);

        return view('admin.esg.index', [
            'pageTitle' => 'ESG Management Dashboard',
            'breadcrumbItems' => $breadcrumbsItems,
            'stats' => $stats,
            'categoryStats' => $categoryStats,
            'recentActivity' => $recentActivity,
        ]);
    }

    /**
     * ESG Questions Management
     */
    public function questions(Request $request)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'ESG Management',
                'url' => route('admin.esg.index'),
                'active' => false
            ],
            [
                'name' => 'Questions Management',
                'url' => route('admin.esg.questions.index'),
                'active' => true
            ],
        ];

        $query = EsgQuestion::query();

        // Filter by category
        if ($request->has('category') && $request->category !== '') {
            $query->where('category', $request->category);
        }

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Search by question text
        if ($request->has('search') && $request->search !== '') {
            $query->where('question_text', 'like', '%' . $request->search . '%');
        }

        $questions = $query->orderBy('category')
                          ->orderBy('sort_order')
                          ->orderBy('id')
                          ->paginate(20);

        $categories = ['environmental', 'social', 'governance'];
        $questionTypes = ['yes_no', 'scale', 'multiple_choice', 'text', 'numeric'];

        $filters = [
            'category' => $request->get('category', ''),
            'status' => $request->get('status', ''),
            'search' => $request->get('search', ''),
        ];

        return view('admin.esg.questions.index', [
            'pageTitle' => 'ESG Questions Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'questions' => $questions,
            'categories' => $categories,
            'questionTypes' => $questionTypes,
            'filters' => $filters,
        ]);
    }

    /**
     * Show form for creating new ESG question
     */
    public function createQuestion()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'ESG Management',
                'url' => route('admin.esg.index'),
                'active' => false
            ],
            [
                'name' => 'Questions Management',
                'url' => route('admin.esg.questions.index'),
                'active' => false
            ],
            [
                'name' => 'Create Question',
                'url' => route('admin.esg.questions.create'),
                'active' => true
            ],
        ];

        $categories = ['environmental', 'social', 'governance'];
        $questionTypes = ['yes_no', 'scale', 'multiple_choice', 'text', 'numeric'];

        return view('admin.esg.questions.create', [
            'pageTitle' => 'Create ESG Question',
            'breadcrumbItems' => $breadcrumbsItems,
            'categories' => $categories,
            'questionTypes' => $questionTypes,
        ]);
    }

    /**
     * Store new ESG question
     */
    public function storeQuestion(Request $request)
    {
        $validated = $request->validate([
            'question_text' => 'required|string|max:500',
            'category' => 'required|in:environmental,social,governance',
            'type' => 'required|in:yes_no,scale,multiple_choice,text,numeric',
            'options' => 'nullable|array',
            'weight' => 'required|integer|min:1|max:20',
            'sort_order' => 'nullable|integer|min:0',
            'is_required' => 'boolean',
            'is_active' => 'boolean',
            'help_text' => 'nullable|string|max:1000',
        ]);

        // Set default sort order if not provided
        if (!isset($validated['sort_order'])) {
            $maxSortOrder = EsgQuestion::where('category', $validated['category'])->max('sort_order') ?? 0;
            $validated['sort_order'] = $maxSortOrder + 1;
        }

        EsgQuestion::create($validated);

        return redirect()->route('admin.esg.questions')
                        ->with('success', 'ESG question created successfully.');
    }

    /**
     * Show form for editing ESG question
     */
    public function editQuestion(EsgQuestion $question)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'ESG Management',
                'url' => route('admin.esg.index'),
                'active' => false
            ],
            [
                'name' => 'Questions Management',
                'url' => route('admin.esg.questions.index'),
                'active' => false
            ],
            [
                'name' => 'Edit Question',
                'url' => route('admin.esg.questions.edit', $question),
                'active' => true
            ],
        ];

        $categories = ['environmental', 'social', 'governance'];
        $questionTypes = ['yes_no', 'scale', 'multiple_choice', 'text', 'numeric'];

        return view('admin.esg.questions.edit', [
            'pageTitle' => 'Edit ESG Question',
            'breadcrumbItems' => $breadcrumbsItems,
            'question' => $question,
            'categories' => $categories,
            'questionTypes' => $questionTypes,
        ]);
    }

    /**
     * Update ESG question
     */
    public function updateQuestion(Request $request, EsgQuestion $question)
    {
        $validated = $request->validate([
            'question_text' => 'required|string|max:500',
            'category' => 'required|in:environmental,social,governance',
            'type' => 'required|in:yes_no,scale,multiple_choice,text,numeric',
            'options' => 'nullable|array',
            'weight' => 'required|integer|min:1|max:20',
            'sort_order' => 'nullable|integer|min:0',
            'is_required' => 'boolean',
            'is_active' => 'boolean',
            'help_text' => 'nullable|string|max:1000',
        ]);

        $question->update($validated);

        return redirect()->route('admin.esg.questions')
                        ->with('success', 'ESG question updated successfully.');
    }

    /**
     * Delete ESG question
     */
    public function destroyQuestion(EsgQuestion $question)
    {
        // Check if question has responses
        if ($question->esgResponses()->exists()) {
            return redirect()->route('admin.esg.questions')
                            ->with('error', 'Cannot delete question that has responses. Deactivate it instead.');
        }

        $question->delete();

        return redirect()->route('admin.esg.questions')
                        ->with('success', 'ESG question deleted successfully.');
    }

    /**
     * ESG Responses Review
     */
    public function responses(Request $request)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'ESG Management',
                'url' => route('admin.esg.index'),
                'active' => false
            ],
            [
                'name' => 'Responses Review',
                'url' => route('admin.esg.responses.index'),
                'active' => true
            ],
        ];

        $query = StartupProfile::whereNotNull('esg_score')
                              ->with(['user', 'esgResponses.esgQuestion']);

        // Filter by score range
        if ($request->has('min_score') && $request->min_score !== '') {
            $query->where('esg_score', '>=', $request->min_score);
        }
        if ($request->has('max_score') && $request->max_score !== '') {
            $query->where('esg_score', '<=', $request->max_score);
        }

        // Filter by completion date
        if ($request->has('from_date') && $request->from_date !== '') {
            $query->whereDate('updated_at', '>=', $request->from_date);
        }
        if ($request->has('to_date') && $request->to_date !== '') {
            $query->whereDate('updated_at', '<=', $request->to_date);
        }

        // Search by company name
        if ($request->has('search') && $request->search !== '') {
            $query->where(function ($q) use ($request) {
                $q->where('company_name', 'like', '%' . $request->search . '%')
                  ->orWhereHas('user', function ($userQuery) use ($request) {
                      $userQuery->where('name', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $responses = $query->orderBy('updated_at', 'desc')->paginate(20);

        return view('admin.esg.responses.index', [
            'pageTitle' => 'ESG Responses Review',
            'breadcrumbItems' => $breadcrumbsItems,
            'responses' => $responses,
            'filters' => $request->only(['min_score', 'max_score', 'from_date', 'to_date', 'search']),
        ]);
    }

    /**
     * View detailed ESG response
     */
    public function viewResponse(StartupProfile $startupProfile)
    {
        $startupProfile->load(['user', 'esgResponses.esgQuestion']);

        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'ESG Management',
                'url' => route('admin.esg.index'),
                'active' => false
            ],
            [
                'name' => 'Responses Review',
                'url' => route('admin.esg.responses'),
                'active' => false
            ],
            [
                'name' => $startupProfile->company_name ?? $startupProfile->user->name,
                'url' => route('admin.esg.responses.view', $startupProfile),
                'active' => true
            ],
        ];

        // Group responses by category
        $responsesByCategory = $startupProfile->esgResponses->groupBy('esgQuestion.category');

        // Calculate detailed scoring
        $scoreData = $this->esgScoringService->calculateScore($startupProfile);

        return view('admin.esg.responses.view', [
            'pageTitle' => 'ESG Response Details',
            'breadcrumbItems' => $breadcrumbsItems,
            'startupProfile' => $startupProfile,
            'responsesByCategory' => $responsesByCategory,
            'scoreData' => $scoreData,
        ]);
    }

    /**
     * ESG Scoring Configuration
     */
    public function configuration()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Dashboard',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'ESG Management',
                'url' => route('admin.esg.index'),
                'active' => false
            ],
            [
                'name' => 'Scoring Configuration',
                'url' => route('admin.esg.configuration'),
                'active' => true
            ],
        ];

        // Get questions grouped by category with their weights
        $questionsByCategory = EsgQuestion::active()
                                         ->orderBy('category')
                                         ->orderBy('sort_order')
                                         ->get()
                                         ->groupBy('category');

        // Calculate category weights
        $categoryWeights = [];
        foreach ($questionsByCategory as $category => $questions) {
            $categoryWeights[$category] = $questions->sum('weight');
        }

        return view('admin.esg.configuration', [
            'pageTitle' => 'ESG Scoring Configuration',
            'breadcrumbItems' => $breadcrumbsItems,
            'questionsByCategory' => $questionsByCategory,
            'categoryWeights' => $categoryWeights,
        ]);
    }

    /**
     * Get category average score
     */
    private function getCategoryAverageScore(string $category): float
    {
        $profiles = StartupProfile::whereNotNull('esg_breakdown')->get();
        
        if ($profiles->isEmpty()) {
            return 0;
        }

        $scores = $profiles->map(function ($profile) use ($category) {
            $breakdown = is_string($profile->esg_breakdown) 
                ? json_decode($profile->esg_breakdown, true) 
                : $profile->esg_breakdown;
            
            return $breakdown[$category] ?? 0;
        })->filter(function ($score) {
            return $score > 0;
        });

        return $scores->isEmpty() ? 0 : $scores->average();
    }
}
